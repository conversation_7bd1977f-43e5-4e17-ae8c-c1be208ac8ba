package com.wexl.erp.fees.model;

import java.util.Arrays;
import java.util.List;

public enum FeeReportType {
    STUDENT_TERM_WISE("student_term_wise", 1, Arrays.asList(0, 1, 3)),
    TOTAL_DUE("total_due", 2, Arrays.asList(0, 1, 3)),
    PAST_DUE("past_due", 2, Arrays.asList(0, 2)),
    FEE_HEAD_MASTER("fee_head_master", 3, Arrays.asList(0, 1, 2, 3));

    private final String reportTypeName;
    private final int headerRowCount;
    private final List<Integer> statusFilters;

    FeeReportType(String reportTypeName, int headerRowCount, List<Integer> statusFilters) {
        this.reportTypeName = reportTypeName;
        this.headerRowCount = headerRowCount;
        this.statusFilters = statusFilters;
    }

    public List<Integer> getStatusFilters() {
        return statusFilters;
    }

    public static FeeReportType fromString(String reportType) {
        if (reportType == null) {
            return TOTAL_DUE;
        }

        for (FeeReportType type : values()) {
            if (type.reportTypeName.equalsIgnoreCase(reportType)) {
                return type;
            }
        }

        return TOTAL_DUE;
    }

    public boolean requiresTwoRowHeaders() {
        return this == TOTAL_DUE || this == PAST_DUE;
    }

    public boolean requiresSingleRowHeaders() {
        return this == STUDENT_TERM_WISE;
    }

    public boolean requiresThreeRowHeaders() {
        return this == FEE_HEAD_MASTER;
    }

    public String getFileNamePrefix() {
        return switch (this) {
            case STUDENT_TERM_WISE -> "student_term_wise_report_";
            case TOTAL_DUE -> "total_due_report_";
            case PAST_DUE -> "past_due_report_";
            case FEE_HEAD_MASTER -> "fee_head_master_report_";
        };
    }
}