package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeeReportType;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianService guardianService;

  private static final List<String> CSV_HEADERS =
      Arrays.asList(
          "STUDENT NAME",
          "FATHER NAME",
          "MOTHER NAME",
          "GUARDIAN NAME",
          "MOBILE NUMBER",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "SECTION NAME",
          "DATE OF ADMISSION");

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {
    generateUnifiedFeeDueReportCsv(orgSlug, request, response);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    currentFeeHeads.addAll(feeHeads);
    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    return students.stream()
        .map(
            student -> {
              List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);
              if ("past_due".equals(reportType)) {
                if (studentFeeHeads == null
                    || studentFeeHeads.stream()
                            .map(FeeHead::getBalanceAmount)
                            .filter(Objects::nonNull)
                            .mapToDouble(Double::doubleValue)
                            .sum()
                        <= 0) {
                  return null;
                }
              }
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, studentFeeHeads);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<String> feeNames = request.feeGroupTypes();

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";

    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .guardianName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
                .findAny()
                .map(guardianService::getGuardianName)
                .orElse(null))
        .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    List<List<String>> csvData = buildCsvBody(reportData, request, currentFeeHeads);
    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(CSV_HEADERS.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    List<String> csvHeaders = csvData.removeFirst();
    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateDynamicHeaders(request, currentFeeHeads);
    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDynamicDataRow(report, request, currentFeeHeads);
      csvBody.add(row);
    }

    return csvBody;
  }

  private List<String> generateDynamicHeaders(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {

    List<String> headers = new ArrayList<>(CSV_HEADERS);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        headers.add(feeGroupDescription);
      } else {
        for (String feeType : feeTypes) {
          headers.add(feeGroupDescription + " " + feeType);
        }
      }
    }

    headers.add("DISCOUNT AMOUNT");
    headers.add("TOTAL DUE AMOUNT");
    //    headers.addAll(List.of("Delete", "EXTENDED DAY", "Fee Remark"));

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructure(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    if (currentFeeHeads != null) {
      for (FeeHead feeHead : currentFeeHeads) {
        if (feeHead.getFeeMaster() != null
            && feeHead.getFeeMaster().getFeeGroup() != null
            && feeHead.getFeeType() != null) {
          String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getName();
          String feeTypeName = feeHead.getFeeType().getName();

          feeGroupStructure
              .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
              .add(feeTypeName);
        }
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private String getFeeGroupDescriptionForStudent(
      Long studentId, String feeTypeName, List<FeeHead> currentFeeHeads) {
    if (currentFeeHeads == null || studentId == null || feeTypeName == null) {
      return "Unknown Fee Group";
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getId() == (studentId)
          && feeHead.getFeeType() != null
          && feeTypeName.equals(feeHead.getFeeType().getName())
          && feeHead.getFeeMaster() != null
          && feeHead.getFeeMaster().getFeeGroup() != null) {
        return feeHead.getFeeMaster().getFeeGroup().getName();
      }
    }
    return "Unknown Fee Group";
  }

  private List<String> buildDynamicDataRow(
      FeeDto.FeeDueReportResponse report,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName() != null ? report.studentName() : "");
    row.add(report.fatherName() != null ? report.fatherName() : "");
    row.add(report.motherName() != null ? report.motherName() : "");
    row.add(report.guardianName() != null ? report.guardianName() : "");
    row.add(report.mobileNumber() != null ? report.mobileNumber() : "");
    row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
    row.add(report.rollNumber() != null ? report.rollNumber() : "");
    row.add(report.sectionName() != null ? report.sectionName() : "");
    row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");

    Map<String, Map<String, Double>> feeGroupAmounts =
        buildFeeGroupAmountsMap(report, currentFeeHeads);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        Double amount =
            feeGroupAmounts.getOrDefault(feeGroupDescription, new HashMap<>()).values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        row.add(String.format("%.0f", amount));
      } else {
        for (String feeType : feeTypes) {
          Double amount =
              feeGroupAmounts
                  .getOrDefault(feeGroupDescription, new HashMap<>())
                  .getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));
    //    row.add("0");
    //    row.add("0");
    //    row.add("");

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeGroupAmountsMap(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    Long studentId = getStudentIdFromReport(report, currentFeeHeads);

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroupDescription =
          getFeeGroupDescriptionForStudent(studentId, detail.feeTypeName(), currentFeeHeads);
      String feeTypeName = detail.feeTypeName();
      Double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private Long getStudentIdFromReport(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    String admissionNumber = report.admissionNumber();
    if (admissionNumber == null) {
      return null;
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getUserInfo() != null
          && admissionNumber.equals(feeHead.getStudent().getUserInfo().getUserName())) {
        return feeHead.getStudent().getId();
      }
    }
    return null;
  }

  public void generateUnifiedFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    FeeReportType reportType = FeeReportType.fromString(request.reportType());
    List<FeeHead> feeHeads = fetchFeeData(orgSlug, request, reportType);

    if (feeHeads.isEmpty()) {
      return;
    }

    List<Student> students = getStudentsBySectionUuids(request);
    Map<String, Object> reportData = buildUnifiedReportData(students, feeHeads, reportType);
    generateUnifiedCsvResponse(reportData, response, reportType, request);
  }


  private List<FeeHead> fetchFeeData(String orgSlug, FeeDto.FeeDueReportRequest request, FeeReportType reportType) {
    List<Student> students = getStudentsBySectionUuids(request);
    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    return feeHeadRepository.findUnifiedFeeReportData(
        orgSlug,
        studentIds,
        reportType.getStatusFilters(),
        request.feeGroupTypes(),
        dateTimeUtil.convertEpochToIso8601(request.fromDate()),
        dateTimeUtil.convertEpochToIso8601(request.toDate())
    );
  }

  private Map<String, String> fetchStudentData(Student student) {
    Map<String, String> studentData = new HashMap<>();

    studentData.put("student_name", userService.getNameByUserInfo(student.getUserInfo()));
    studentData.put("admission_number", student.getUserInfo().getUserName());
    studentData.put("roll_number", student.getClassRollNumber());
    studentData.put("section_name", student.getSection() != null ? student.getSection().getName() : "");
    studentData.put("date_of_admission",
        student.getCreatedAt() != null
            ? student.getCreatedAt().toLocalDateTime().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
            : "");

    studentData.put("father_name",
        student.getGuardians().stream()
            .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
            .findAny()
            .map(guardianService::getGuardianName)
            .orElse(""));

    studentData.put("mother_name",
        student.getGuardians().stream()
            .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
            .findAny()
            .map(guardianService::getGuardianName)
            .orElse(""));

    studentData.put("guardian_name",
        student.getGuardians().stream()
            .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
            .findAny()
            .map(guardianService::getGuardianName)
            .orElse(""));

    studentData.put("mobile_number", student.getUserInfo().getMobileNumber());

    return studentData;
  }

  private Map<String, Map<String, Double>> calculateFeeAmounts(List<FeeHead> studentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    for (FeeHead feeHead : studentFeeHeads) {
      String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getDescription();
      String feeTypeName = feeHead.getFeeType().getName();
      Double amount = feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private List<List<String>> buildDynamicHeaders(FeeReportType reportType, List<FeeHead> feeHeads, FeeDto.FeeDueReportRequest request) {
    List<List<String>> headers = new ArrayList<>();

    List<String> studentFields = Arrays.asList(
        "STUDENT NAME", "FATHER NAME", "MOTHER NAME", "GUARDIAN NAME", "MOBILE NUMBER",
        "ADMISSION NUMBER", "ROLL NUMBER", "SECTION NAME", "DATE OF ADMISSION"
    );

    if (reportType.requiresSingleRowHeaders()) {
      List<String> headerRow = new ArrayList<>(studentFields);

      Set<String> feeTypes = feeHeads.stream()
          .map(fh -> fh.getFeeType().getName())
          .collect(Collectors.toCollection(LinkedHashSet::new));

      headerRow.addAll(feeTypes);
      headerRow.addAll(Arrays.asList("DISCOUNT AMOUNT", "TOTAL DUE AMOUNT"));
      headers.add(headerRow);

    } else if (reportType.requiresTwoRowHeaders()) {
      Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructureFromFeeHeads(feeHeads, request);

      List<String> row1 = new ArrayList<>(studentFields);
      List<String> row2 = new ArrayList<>(Collections.nCopies(studentFields.size(), ""));

      for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
        String feeGroupDesc = entry.getKey();
        Set<String> feeTypes = entry.getValue();

        row1.add(feeGroupDesc);
        if (feeTypes.size() > 1) {
          row1.addAll(Collections.nCopies(feeTypes.size() - 1, ""));
        }

        row2.addAll(feeTypes);
      }

      row1.addAll(Arrays.asList("DISCOUNT AMOUNT", "TOTAL DUE AMOUNT"));
      row2.addAll(Arrays.asList("", ""));

      headers.add(row1);
      headers.add(row2);

    } else if (reportType.requiresThreeRowHeaders()) {
      List<String> row1 = new ArrayList<>(studentFields);
      List<String> row2 = new ArrayList<>(Collections.nCopies(studentFields.size(), ""));
      List<String> row3 = new ArrayList<>(Collections.nCopies(studentFields.size(), ""));

      row1.addAll(Arrays.asList("Fee Applicable", "Fee Collected", "Total Paid", "Total Due"));
      row2.addAll(Arrays.asList("Fee Types + Discounts + Grand Total", "Fee Head IDs", "Amount", "Balance"));
      row3.addAll(Arrays.asList("", "Date + Receipt Number + Amount", "", ""));

      headers.add(row1);
      headers.add(row2);
      headers.add(row3);
    }

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructureFromFeeHeads(List<FeeHead> feeHeads, FeeDto.FeeDueReportRequest request) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    for (FeeHead feeHead : feeHeads) {
      if (feeHead.getFeeMaster() != null && feeHead.getFeeMaster().getFeeGroup() != null && feeHead.getFeeType() != null) {
        String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getDescription();
        String feeTypeName = feeHead.getFeeType().getName();

        feeGroupStructure
            .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
            .add(feeTypeName);
      }
    }

    if (request.feeGroupTypes() != null && !request.feeGroupTypes().isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : request.feeGroupTypes()) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private Map<String, Object> buildUnifiedReportData(List<Student> students, List<FeeHead> feeHeads, FeeReportType reportType) {
    Map<String, Object> reportData = new HashMap<>();

    Map<Student, List<FeeHead>> feeHeadsByStudent = feeHeads.stream()
        .collect(Collectors.groupingBy(FeeHead::getStudent));

    List<Map<String, Object>> studentReports = new ArrayList<>();

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);

      Map<String, Object> studentReport = new HashMap<>();
      studentReport.put("studentData", fetchStudentData(student));
      studentReport.put("feeAmounts", studentFeeHeads != null ? calculateFeeAmounts(studentFeeHeads) : new HashMap<>());
      studentReport.put("discountAmount", calculateDiscount(student));
      studentReport.put("totalDueAmount", studentFeeHeads != null ?
          studentFeeHeads.stream().mapToDouble(fh -> fh.getBalanceAmount() != null ? fh.getBalanceAmount() : 0.0).sum() : 0.0);

      studentReports.add(studentReport);
    }

    reportData.put("students", studentReports);
    reportData.put("feeHeads", feeHeads);
    return reportData;
  }

  private void generateUnifiedCsvResponse(Map<String, Object> reportData, HttpServletResponse response,
                                        FeeReportType reportType, FeeDto.FeeDueReportRequest request) {

    String fileName = getUnifiedFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    @SuppressWarnings("unchecked")
    List<Map<String, Object>> students = (List<Map<String, Object>>) reportData.get("students");
    @SuppressWarnings("unchecked")
    List<FeeHead> feeHeads = (List<FeeHead>) reportData.get("feeHeads");

    if (students.isEmpty()) {
      return;
    }

    List<List<String>> headers = buildDynamicHeaders(reportType, feeHeads, request);
    List<List<String>> csvData = generateCsvContent(students, feeHeads, reportType, request);

    List<List<String>> fullCsvData = new ArrayList<>(headers);
    fullCsvData.addAll(csvData);

    String[] headerArray = fullCsvData.get(0).toArray(new String[0]);
    List<List<String>> dataRows = fullCsvData.subList(1, fullCsvData.size());

    CsvUtils.generateCsv(headerArray, dataRows, response);
  }

  private List<List<String>> generateCsvContent(List<Map<String, Object>> students, List<FeeHead> feeHeads,
                                              FeeReportType reportType, FeeDto.FeeDueReportRequest request) {
    List<List<String>> csvData = new ArrayList<>();

    for (Map<String, Object> studentReport : students) {
      @SuppressWarnings("unchecked")
      Map<String, String> studentData = (Map<String, String>) studentReport.get("studentData");
      @SuppressWarnings("unchecked")
      Map<String, Map<String, Double>> feeAmounts = (Map<String, Map<String, Double>>) studentReport.get("feeAmounts");
      Double discountAmount = (Double) studentReport.get("discountAmount");
      Double totalDueAmount = (Double) studentReport.get("totalDueAmount");

      List<String> row = buildStudentDataRow(studentData, feeAmounts, discountAmount, totalDueAmount,
                                           feeHeads, reportType, request);
      csvData.add(row);
    }

    return csvData;
  }

  private List<String> buildStudentDataRow(Map<String, String> studentData, Map<String, Map<String, Double>> feeAmounts,
                                         Double discountAmount, Double totalDueAmount, List<FeeHead> feeHeads,
                                         FeeReportType reportType, FeeDto.FeeDueReportRequest request) {
    List<String> row = new ArrayList<>();

    row.add(studentData.getOrDefault("student_name", ""));
    row.add(studentData.getOrDefault("father_name", ""));
    row.add(studentData.getOrDefault("mother_name", ""));
    row.add(studentData.getOrDefault("guardian_name", ""));
    row.add(studentData.getOrDefault("mobile_number", ""));
    row.add(studentData.getOrDefault("admission_number", ""));
    row.add(studentData.getOrDefault("roll_number", ""));
    row.add(studentData.getOrDefault("section_name", ""));
    row.add(studentData.getOrDefault("date_of_admission", ""));

    if (reportType.requiresSingleRowHeaders()) {
      Set<String> feeTypes = feeHeads.stream()
          .map(fh -> fh.getFeeType().getName())
          .collect(Collectors.toCollection(LinkedHashSet::new));

      for (String feeType : feeTypes) {
        Double amount = feeAmounts.values().stream()
            .mapToDouble(feeTypeMap -> feeTypeMap.getOrDefault(feeType, 0.0))
            .sum();
        row.add(String.format("%.0f", amount));
      }

    } else if (reportType.requiresTwoRowHeaders()) {
      Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructureFromFeeHeads(feeHeads, request);

      for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
        String feeGroupDesc = entry.getKey();
        Set<String> feeTypes = entry.getValue();

        for (String feeType : feeTypes) {
          Double amount = feeAmounts.getOrDefault(feeGroupDesc, new HashMap<>()).getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }

    } else if (reportType.requiresThreeRowHeaders()) {
      row.add("0");
      row.add("0");
      row.add("0");
      row.add(String.format("%.0f", totalDueAmount));
    }

    row.add(String.format("%.0f", discountAmount != null ? discountAmount : 0.0));
    row.add(String.format("%.0f", totalDueAmount != null ? totalDueAmount : 0.0));

    return row;
  }

  private String getUnifiedFileName(FeeReportType reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return reportType.getFileNamePrefix() + timestamp + ".csv";
  }
}
